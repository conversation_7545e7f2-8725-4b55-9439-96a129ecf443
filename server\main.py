"""
Valorant Strategy Board - Collaboration Server
FastAPI + Socket.IO server for real-time collaboration
"""

from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import socketio
import uvicorn
import os
import logging
from typing import Dict, Set
import asyncio
from datetime import datetime
import json

from session_manager import SessionManager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(title="Valorant Strategy Board Server", version="1.0.0")

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify your domain
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create Socket.IO server
sio = socketio.AsyncServer(
    cors_allowed_origins="*",
    logger=True,
    engineio_logger=True
)

# Combine FastAPI and Socket.IO
socket_app = socketio.ASGIApp(sio, app)

# Initialize session manager
session_manager = SessionManager()

# Store connected clients
connected_clients: Dict[str, Dict] = {}


@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "message": "Valorant Strategy Board Server",
        "status": "running",
        "active_sessions": len(session_manager.sessions),
        "connected_clients": len(connected_clients)
    }


@app.get("/health")
async def health_check():
    """Health check for deployment platforms"""
    return {"status": "healthy"}

@app.head("/")
async def root_head():
    """Handle HEAD requests for health checks"""
    return {"status": "ok"}

@app.head("/health")
async def health_check_head():
    """Handle HEAD requests for health checks"""
    return {"status": "healthy"}


@sio.event
async def connect(sid, environ):
    """Handle client connection"""
    logger.info(f"Client connected: {sid}")
    
    # Initialize client data
    connected_clients[sid] = {
        "user_id": None,
        "user_name": None,
        "session_code": None,
        "connected_at": datetime.now().isoformat()
    }
    
    await sio.emit('connected', {'client_id': sid}, room=sid)


@sio.event
async def disconnect(sid):
    """Handle client disconnection"""
    logger.info(f"Client disconnected: {sid}")
    
    if sid in connected_clients:
        client_data = connected_clients[sid]
        session_code = client_data.get('session_code')
        
        # Remove from session if they were in one
        if session_code:
            await leave_session_internal(sid, session_code)
        
        # Remove from connected clients
        del connected_clients[sid]


@sio.event
async def create_session(sid, data):
    """Create a new collaboration session"""
    try:
        session_name = data.get('session_name', 'Strategy Session')
        user_name = data.get('user_name', f'User_{sid[:6]}')
        user_id = data.get('user_id', f'user_{sid}')
        
        # Create session
        session_code = session_manager.create_session(session_name, sid, user_id, user_name)
        
        # Update client data
        connected_clients[sid].update({
            "user_id": user_id,
            "user_name": user_name,
            "session_code": session_code
        })
        
        # Join the socket room
        await sio.enter_room(sid, session_code)
        
        logger.info(f"Session created: {session_code} by {user_name}")
        
        await sio.emit('session_created', {
            'session_code': session_code,
            'session_name': session_name,
            'user_id': user_id,
            'user_name': user_name,
            'is_host': True
        }, room=sid)
        
    except Exception as e:
        logger.error(f"Error creating session: {e}")
        await sio.emit('error', {'message': str(e)}, room=sid)


@sio.event
async def join_session(sid, data):
    """Join an existing collaboration session"""
    try:
        session_code = data.get('session_code', '').upper()
        user_name = data.get('user_name', f'User_{sid[:6]}')
        user_id = data.get('user_id', f'user_{sid}')
        
        if not session_code or len(session_code) != 6:
            raise ValueError("Invalid session code")
        
        # Check if session exists
        if not session_manager.session_exists(session_code):
            raise ValueError("Session not found")
        
        # Join session
        session_manager.join_session(session_code, sid, user_id, user_name)
        
        # Update client data
        connected_clients[sid].update({
            "user_id": user_id,
            "user_name": user_name,
            "session_code": session_code
        })
        
        # Join the socket room
        await sio.enter_room(sid, session_code)
        
        # Get session info
        session_info = session_manager.get_session_info(session_code)
        
        logger.info(f"User {user_name} joined session: {session_code}")
        
        # Notify the user they joined successfully
        await sio.emit('session_joined', {
            'session_code': session_code,
            'session_name': session_info['session_name'],
            'user_id': user_id,
            'user_name': user_name,
            'is_host': False,
            'connected_users': session_info['users']
        }, room=sid)
        
        # Notify other users in the session
        await sio.emit('user_joined', {
            'user_id': user_id,
            'user_name': user_name,
            'joined_at': datetime.now().isoformat()
        }, room=session_code, skip_sid=sid)
        
    except Exception as e:
        logger.error(f"Error joining session: {e}")
        await sio.emit('error', {'message': str(e)}, room=sid)


@sio.event
async def leave_session(sid, data):
    """Leave current session"""
    try:
        session_code = data.get('session_code')
        if not session_code and sid in connected_clients:
            session_code = connected_clients[sid].get('session_code')
        
        if session_code:
            await leave_session_internal(sid, session_code)
            
    except Exception as e:
        logger.error(f"Error leaving session: {e}")
        await sio.emit('error', {'message': str(e)}, room=sid)


async def leave_session_internal(sid, session_code):
    """Internal function to handle leaving a session"""
    if sid in connected_clients:
        client_data = connected_clients[sid]
        user_id = client_data.get('user_id')
        user_name = client_data.get('user_name')
        
        # Remove from session
        session_manager.leave_session(session_code, sid)
        
        # Leave socket room
        await sio.leave_room(sid, session_code)
        
        # Update client data
        connected_clients[sid]['session_code'] = None
        
        logger.info(f"User {user_name} left session: {session_code}")
        
        # Notify other users
        await sio.emit('user_left', {
            'user_id': user_id,
            'user_name': user_name,
            'left_at': datetime.now().isoformat()
        }, room=session_code)
        
        # Notify the user they left
        await sio.emit('session_left', {
            'session_code': session_code
        }, room=sid)


@sio.event
async def canvas_change(sid, data):
    """Handle canvas changes and broadcast to session"""
    try:
        if sid not in connected_clients:
            return
        
        session_code = connected_clients[sid].get('session_code')
        if not session_code:
            return
        
        user_id = connected_clients[sid].get('user_id')
        user_name = connected_clients[sid].get('user_name')
        
        # Add metadata to the change
        change_data = {
            **data,
            'from_user_id': user_id,
            'from_user_name': user_name,
            'timestamp': datetime.now().isoformat()
        }
        
        # Broadcast to all other users in the session
        await sio.emit('canvas_change', change_data, room=session_code, skip_sid=sid)
        
    except Exception as e:
        logger.error(f"Error handling canvas change: {e}")


@sio.event
async def cursor_update(sid, data):
    """Handle cursor position updates"""
    try:
        if sid not in connected_clients:
            return
        
        session_code = connected_clients[sid].get('session_code')
        if not session_code:
            return
        
        user_id = connected_clients[sid].get('user_id')
        user_name = connected_clients[sid].get('user_name')
        
        # Add user info to cursor data
        cursor_data = {
            **data,
            'user_id': user_id,
            'user_name': user_name,
            'timestamp': datetime.now().isoformat()
        }
        
        # Broadcast to all other users in the session
        await sio.emit('cursor_update', cursor_data, room=session_code, skip_sid=sid)
        
    except Exception as e:
        logger.error(f"Error handling cursor update: {e}")


if __name__ == "__main__":
    port = int(os.environ.get("PORT", 8000))
    uvicorn.run(socket_app, host="0.0.0.0", port=port)
