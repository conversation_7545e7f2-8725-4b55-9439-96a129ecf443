<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .log {
            background: #f0f0f0;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            max-height: 400px;
            overflow-y: auto;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            font-size: 16px;
        }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <h1>WebSocket Connection Test</h1>
    <p>Testing connection to: <strong>wss://valorant-strategy-board-server.onrender.com</strong></p>
    
    <button onclick="testConnection()">Test WebSocket Connection</button>
    <button onclick="clearLog()">Clear Log</button>
    
    <div id="log" class="log"></div>

    <script>
        let socket = null;
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            logDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        function testConnection() {
            log('Starting WebSocket connection test...', 'info');
            
            if (socket) {
                log('Closing existing connection...', 'info');
                socket.close();
            }
            
            try {
                const url = 'wss://valorant-strategy-board-server.onrender.com/';
                log(`Attempting to connect to: ${url}`, 'info');
                
                socket = new WebSocket(url);
                
                socket.onopen = function(event) {
                    log('✅ WebSocket connection opened successfully!', 'success');
                    log('Sending test message...', 'info');
                    
                    // Send a test message
                    const testMessage = {
                        type: 'test',
                        message: 'Hello from test client',
                        timestamp: new Date().toISOString()
                    };
                    socket.send(JSON.stringify(testMessage));
                };
                
                socket.onmessage = function(event) {
                    log(`📨 Received message: ${event.data}`, 'success');
                };
                
                socket.onerror = function(error) {
                    log(`❌ WebSocket error: ${error}`, 'error');
                    console.error('WebSocket error:', error);
                };
                
                socket.onclose = function(event) {
                    log(`🔌 WebSocket connection closed. Code: ${event.code}, Reason: ${event.reason}`, 'info');
                };
                
            } catch (error) {
                log(`❌ Failed to create WebSocket: ${error.message}`, 'error');
                console.error('WebSocket creation error:', error);
            }
        }
        
        // Auto-test on page load
        window.onload = function() {
            log('WebSocket test page loaded', 'info');
            log('Click "Test WebSocket Connection" to start the test', 'info');
        };
    </script>
</body>
</html>
