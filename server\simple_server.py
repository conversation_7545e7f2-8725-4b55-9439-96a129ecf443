"""
Simple WebSocket Server for Valorant Strategy Board
Using websockets library for better compatibility
"""

import asyncio
import websockets
import json
import logging
from datetime import datetime
import random
import string
from typing import Dict, Set
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SimpleCollaborationServer:
    def __init__(self):
        self.sessions: Dict[str, Dict] = {}
        self.clients: Dict[str, Dict] = {}  # websocket -> client info
        
    def generate_session_code(self) -> str:
        """Generate a unique 6-character session code"""
        chars = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789'
        while True:
            code = ''.join(random.choices(chars, k=6))
            if code not in self.sessions:
                return code
    
    async def register_client(self, websocket, client_id: str):
        """Register a new client"""
        self.clients[websocket] = {
            'client_id': client_id,
            'user_id': None,
            'user_name': None,
            'session_code': None,
            'connected_at': datetime.now().isoformat()
        }
        logger.info(f"Client registered: {client_id}")
    
    async def unregister_client(self, websocket):
        """Unregister a client"""
        if websocket in self.clients:
            client_info = self.clients[websocket]
            session_code = client_info.get('session_code')
            
            # Remove from session if they were in one
            if session_code and session_code in self.sessions:
                await self.leave_session(websocket, session_code)
            
            del self.clients[websocket]
            logger.info(f"Client unregistered: {client_info.get('client_id')}")
    
    async def create_session(self, websocket, data):
        """Create a new collaboration session"""
        try:
            session_name = data.get('session_name', 'Strategy Session')
            user_name = data.get('user_name', 'Unknown User')
            user_id = data.get('user_id', f'user_{datetime.now().timestamp()}')
            
            session_code = self.generate_session_code()
            
            # Create session
            self.sessions[session_code] = {
                'session_name': session_name,
                'host_user_id': user_id,
                'host_user_name': user_name,
                'created_at': datetime.now().isoformat(),
                'users': {
                    user_id: {
                        'user_id': user_id,
                        'user_name': user_name,
                        'websocket': websocket,
                        'joined_at': datetime.now().isoformat(),
                        'is_host': True
                    }
                },
                'last_activity': datetime.now().isoformat()
            }
            
            # Update client info
            if websocket in self.clients:
                self.clients[websocket].update({
                    'user_id': user_id,
                    'user_name': user_name,
                    'session_code': session_code
                })
            
            # Send response
            response = {
                'type': 'session_created',
                'session_code': session_code,
                'session_name': session_name,
                'user_id': user_id,
                'user_name': user_name,
                'is_host': True
            }
            
            await websocket.send(json.dumps(response))
            logger.info(f"Session created: {session_code} by {user_name}")
            
        except Exception as e:
            logger.error(f"Error creating session: {e}")
            await self.send_error(websocket, str(e))
    
    async def join_session(self, websocket, data):
        """Join an existing session"""
        try:
            session_code = data.get('session_code', '').upper()
            user_name = data.get('user_name', 'Unknown User')
            user_id = data.get('user_id', f'user_{datetime.now().timestamp()}')
            
            if not session_code or len(session_code) != 6:
                raise ValueError("Invalid session code")
            
            if session_code not in self.sessions:
                raise ValueError("Session not found")
            
            session = self.sessions[session_code]
            
            # Add user to session
            session['users'][user_id] = {
                'user_id': user_id,
                'user_name': user_name,
                'websocket': websocket,
                'joined_at': datetime.now().isoformat(),
                'is_host': False
            }
            
            # Update client info
            if websocket in self.clients:
                self.clients[websocket].update({
                    'user_id': user_id,
                    'user_name': user_name,
                    'session_code': session_code
                })
            
            # Send response to joining user
            response = {
                'type': 'session_joined',
                'session_code': session_code,
                'session_name': session['session_name'],
                'user_id': user_id,
                'user_name': user_name,
                'is_host': False,
                'connected_users': [
                    {
                        'user_id': u['user_id'],
                        'user_name': u['user_name'],
                        'joined_at': u['joined_at'],
                        'is_host': u['is_host']
                    }
                    for u in session['users'].values()
                ]
            }
            
            await websocket.send(json.dumps(response))
            
            # Notify other users
            user_joined_msg = {
                'type': 'user_joined',
                'user_id': user_id,
                'user_name': user_name,
                'joined_at': datetime.now().isoformat()
            }
            
            await self.broadcast_to_session(session_code, user_joined_msg, exclude_websocket=websocket)
            logger.info(f"User {user_name} joined session: {session_code}")
            
        except Exception as e:
            logger.error(f"Error joining session: {e}")
            await self.send_error(websocket, str(e))
    
    async def leave_session(self, websocket, session_code):
        """Leave a session"""
        try:
            if session_code not in self.sessions:
                return
            
            session = self.sessions[session_code]
            client_info = self.clients.get(websocket, {})
            user_id = client_info.get('user_id')
            user_name = client_info.get('user_name')
            
            if user_id and user_id in session['users']:
                # Remove user from session
                del session['users'][user_id]
                
                # Notify other users
                user_left_msg = {
                    'type': 'user_left',
                    'user_id': user_id,
                    'user_name': user_name,
                    'left_at': datetime.now().isoformat()
                }
                
                await self.broadcast_to_session(session_code, user_left_msg)
                
                # Clean up empty session
                if not session['users']:
                    del self.sessions[session_code]
                    logger.info(f"Cleaned up empty session: {session_code}")
                
                logger.info(f"User {user_name} left session: {session_code}")
            
        except Exception as e:
            logger.error(f"Error leaving session: {e}")
    
    async def handle_canvas_change(self, websocket, data):
        """Handle canvas changes and broadcast to session"""
        try:
            client_info = self.clients.get(websocket, {})
            session_code = client_info.get('session_code')

            logger.info(f"Handling canvas change from {client_info.get('user_name')} in session {session_code}")
            logger.info(f"Canvas change data: {data}")

            if not session_code:
                logger.warning("No session code found for canvas change")
                return

            # Add metadata
            change_data = {
                **data,
                'from_user_id': client_info.get('user_id'),
                'from_user_name': client_info.get('user_name'),
                'timestamp': datetime.now().isoformat()
            }

            logger.info(f"Broadcasting canvas change to session {session_code}")
            # Broadcast to session
            await self.broadcast_to_session(session_code, change_data, exclude_websocket=websocket)

        except Exception as e:
            logger.error(f"Error handling canvas change: {e}")
    
    async def handle_cursor_update(self, websocket, data):
        """Handle cursor updates"""
        try:
            client_info = self.clients.get(websocket, {})
            session_code = client_info.get('session_code')
            
            if not session_code:
                return
            
            # Add user info
            cursor_data = {
                **data,
                'type': 'cursor_update',
                'user_id': client_info.get('user_id'),
                'user_name': client_info.get('user_name'),
                'timestamp': datetime.now().isoformat()
            }
            
            # Broadcast to session
            await self.broadcast_to_session(session_code, cursor_data, exclude_websocket=websocket)
            
        except Exception as e:
            logger.error(f"Error handling cursor update: {e}")
    
    async def broadcast_to_session(self, session_code, message, exclude_websocket=None):
        """Broadcast message to all users in a session"""
        if session_code not in self.sessions:
            return
        
        session = self.sessions[session_code]
        message_str = json.dumps(message)
        
        for user_data in session['users'].values():
            websocket = user_data['websocket']
            if websocket != exclude_websocket:
                try:
                    await websocket.send(message_str)
                except Exception as e:
                    logger.error(f"Error sending message to user: {e}")
    
    async def send_error(self, websocket, message):
        """Send error message to client"""
        error_msg = {
            'type': 'error',
            'message': message
        }
        try:
            await websocket.send(json.dumps(error_msg))
        except Exception as e:
            logger.error(f"Error sending error message: {e}")
    
    async def handle_message(self, websocket, message):
        """Handle incoming WebSocket message"""
        try:
            data = json.loads(message)
            message_type = data.get('type')
            
            if message_type == 'create_session':
                await self.create_session(websocket, data)
            elif message_type == 'join_session':
                await self.join_session(websocket, data)
            elif message_type == 'leave_session':
                session_code = data.get('session_code') or self.clients.get(websocket, {}).get('session_code')
                if session_code:
                    await self.leave_session(websocket, session_code)
            elif message_type == 'canvas_change' or message_type == 'canvas-change':
                await self.handle_canvas_change(websocket, data)
            elif message_type == 'cursor_update' or message_type == 'cursor-update':
                await self.handle_cursor_update(websocket, data)
            elif message_type == 'map_change' or message_type == 'map-change':
                await self.handle_canvas_change(websocket, data)  # Treat map changes like canvas changes
            elif message_type == 'canvas-sync-request' or message_type == 'canvas_sync_request':
                await self.handle_canvas_change(websocket, data)  # Treat sync requests like canvas changes
            elif message_type == 'canvas-sync-data' or message_type == 'canvas_sync_data':
                await self.handle_canvas_change(websocket, data)  # Treat sync data like canvas changes
            else:
                logger.warning(f"Unknown message type: {message_type}")
                
        except json.JSONDecodeError:
            logger.error("Invalid JSON received")
            await self.send_error(websocket, "Invalid JSON format")
        except Exception as e:
            logger.error(f"Error handling message: {e}")
            await self.send_error(websocket, str(e))

# Global server instance
server = SimpleCollaborationServer()

async def handle_client(websocket):
    """Handle WebSocket client connection"""
    client_id = f"client_{datetime.now().timestamp()}"
    client_ip = websocket.remote_address[0] if websocket.remote_address else "unknown"

    try:
        logger.info(f"New WebSocket connection from {client_ip}, assigning ID: {client_id}")
        await server.register_client(websocket, client_id)

        # Send connection confirmation
        await websocket.send(json.dumps({
            'type': 'connected',
            'client_id': client_id
        }))
        logger.info(f"Sent connection confirmation to {client_id}")

        async for message in websocket:
            logger.info(f"Received message from {client_id}: {message[:100]}...")
            await server.handle_message(websocket, message)

    except websockets.exceptions.ConnectionClosed:
        logger.info(f"Client {client_id} disconnected normally")
    except Exception as e:
        logger.error(f"Error handling client {client_id}: {e}")
    finally:
        await server.unregister_client(websocket)
        logger.info(f"Cleaned up client {client_id}")

async def health_check_handler(path, request_headers):
    """Handle HTTP health check requests"""
    logger.info(f"🔍 Incoming request - Path: {path}")
    logger.info(f"📋 Raw request headers: {request_headers}")
    logger.info(f"📋 Headers type: {type(request_headers)}")

    try:
        # Handle different header formats
        headers_dict = {}
        if hasattr(request_headers, 'items'):
            # If it's a dict-like object
            headers_dict = {k.lower(): v.lower() for k, v in request_headers.items()}
        elif isinstance(request_headers, (list, tuple)):
            # If it's a list of tuples
            headers_dict = {name.lower(): value.lower() for name, value in request_headers}
        else:
            # Try to convert to dict
            headers_dict = dict(request_headers)
            headers_dict = {k.lower(): v.lower() for k, v in headers_dict.items()}

        logger.info(f"📋 Processed headers: {headers_dict}")

        # Check for WebSocket upgrade headers
        upgrade_header = headers_dict.get('upgrade', '')
        connection_header = headers_dict.get('connection', '')

        logger.info(f"🔧 Upgrade header: '{upgrade_header}'")
        logger.info(f"🔗 Connection header: '{connection_header}'")

        # Check if this is a WebSocket upgrade request
        if upgrade_header == 'websocket' and 'upgrade' in connection_header:
            # This is a WebSocket request, let websockets handle it
            logger.info("🚀 WebSocket upgrade request detected! Passing to websockets handler...")
            return None

    except Exception as e:
        logger.error(f"❌ Error processing headers: {e}")
        # If we can't process headers, assume it's not a WebSocket request

    # This is a regular HTTP request, handle health checks
    if path == "/" or path == "/health":
        # Return HTTP 200 OK for health checks
        logger.info("💚 Responding to health check request")
        return (200, [
            ("Content-Type", "application/json"),
            ("Access-Control-Allow-Origin", "*"),
            ("Access-Control-Allow-Methods", "GET, POST, OPTIONS"),
            ("Access-Control-Allow-Headers", "Content-Type, Authorization")
        ], b'{"status": "healthy", "service": "valorant-strategy-board", "websocket": "enabled"}')

    # For other paths, return 404
    logger.info(f"❓ Unknown path requested: {path}")
    return (404, [("Content-Type", "text/plain")], b'Not Found')

async def main():
    """Start the WebSocket server"""
    port = int(os.environ.get("PORT", 8000))
    host = "0.0.0.0"  # Listen on all interfaces for deployment
    logger.info(f"Starting WebSocket server on {host}:{port}")

    # Configure WebSocket server with cloud platform compatibility
    try:
        async with websockets.serve(
            handle_client,
            host,
            port,
            process_request=health_check_handler,
            # Cloud platform compatibility settings
            ping_interval=30,
            ping_timeout=10,
            close_timeout=10,
            max_size=2**20,  # 1MB max message size
            max_queue=32,
            # Allow all origins for CORS
            origins=None,
            # Add extra headers for better compatibility
            extra_headers=[
                ("Access-Control-Allow-Origin", "*"),
                ("Access-Control-Allow-Methods", "GET, POST, OPTIONS"),
                ("Access-Control-Allow-Headers", "Content-Type, Authorization, Upgrade, Connection"),
            ],
            # Compression settings
            compression=None,  # Disable compression for better compatibility
            # Server header
            server_header="WebSocket-Server/1.0"
        ):
            logger.info(f"✅ WebSocket server successfully started on {host}:{port}")
            logger.info(f"🌐 Server ready to accept WebSocket connections")
            logger.info(f"🔗 WebSocket URL: ws://{host}:{port}")
            logger.info(f"🔗 Secure WebSocket URL: wss://{host}:{port}")
            await asyncio.Future()  # Run forever
    except Exception as e:
        logger.error(f"❌ Failed to start WebSocket server: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
